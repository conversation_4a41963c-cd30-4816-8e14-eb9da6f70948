{"name": "imponeer/queue-interop-connection-factory-helper", "description": "Helper that creates queue-interop connection factory based on DSN", "type": "library", "license": "MIT", "minimum-stability": "dev", "prefer-stable": true, "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.3", "queue-interop/queue-interop": "^0.8"}, "require-dev": {"phpunit/phpunit": "^10.0", "jchook/phpunit-assert-throws": "^1.0"}, "config": {"optimize-autoloader": true, "sort-packages": true}, "autoload": {"psr-4": {"Imponeer\\QueueInteropConnectionFactoryHelper\\": "src/"}}}