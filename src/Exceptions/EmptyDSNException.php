<?php

declare(strict_types=1);

namespace Imponeer\QueueInteropConnectionFactoryHelper\Exceptions;

use Exception;

/**
 * Exception thrown when DSN was empty
 *
 * @package Imponeer\QueueInteropConnectionFactoryHelper\Exceptions
 */
final class EmptyDSNException extends Exception
{
    /**
     * EmptyDSNException constructor.
     *
     * @param string $message Exception message
     * @param int $code Error code
     * @param Exception|null $previous Previous exception
     */
    public function __construct(
        string $message = 'DSN cannot be empty',
        int $code = 0,
        ?Exception $previous = null
    ) {
        parent::__construct($message, $code, $previous);
    }
}